<extend name="Base@Common/listtable" />
<block name="title">
    <title>员工列表</title>
</block>
<block name="css">
    <style type="text/css">
        .query-box>.form-group.query-group{
            margin-bottom: 0;
        }
        .avatar {
            border-radius: 50%;
        }
        .imgbox {
            width: 150px;
            text-align: center;
        }

        .imgbox .upbtn {
            width: 100px;
            height: 100px;
            border: 1px solid #e2e2e2;
            cursor: pointer;
            position: relative;
            margin: 0 auto;
        }

        .imgbox .upbtn .uploadimg {
            width: 100%;
            height: 100%;
        }

        .change-img {
            width: auto;
            padding-top: 36px;
            padding-bottom: 12px;
        }

        .change-tip {
            font-size: 12px;
            color: #000;
            text-align: left;
        }

        .imgbox .upbtn input {
            opacity: 0;
            overflow: hidden;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .spa-page-wrap {
            display: none;
        }

        #edit-optometrist-form .control-label {
            padding: 7px 0 0;
        }
        .none {
            display: none;
        }
    </style>
</block>
<block name="js">
    <script type="text/javascript" src="/manager/js/clipBoard.min.js"></script>
    <script src="/clientClub/js/qrcode3.js"></script>
    <script type="text/javascript">
        inf_dtbl.cols = [{
            tit: '头像',
            rep: function (d) {
                var src = d.c1 || '/ug/mall/assest/images/doctor/<EMAIL>';
                return '<img src="' + src + '" width="66" height="66" class="avatar">';
            }
        }, {
            col: 'c7',
            tit: '员工ID'
        }, {
            col: 'c0',
            tit: '名称'
        }, {
            col: 'c4',
            tit: '性别'
        }, {
            tit: '年龄',
            rep: function (d) {
                return getAge(d.c5);
            }
        }, {
            col: 'c2',
            tit: '简介',
            rep: function (d) {
                if (d.c2 == null) {
                    return '暂无简介';
                } else {
                    return d.c2.substring(0, 20);
                }
            }
        }, {
            col: 'c6',
            tit: '角色'
        }, {
            col: 'c3',
            tit: '时间',
            rep: function (d) {
                return d.c3.date('yyyy-MM-dd');
            }
        }];


        inf_dtbl.menus.dynamic = function (d) {
            var menus = [];
            menus.push({
                tag: 'edit',
                title: '编辑',
                icon: 'glyphicon glyphicon-paperclip'
            });
            menus.push({
                tag: 'del',
                title: '删除',
                icon: 'glyphicon glyphicon-trash'
            });
            return menus;
        };

        inf_ext.menus.callback = function (tag, d) {
            switch (tag) {
                case 'edit':
                    edit(d, $(this));
                    break;
                // case 'del':
                //     del(d, $(this));
                //     break;
                default :
                    break;
            }
        };

        function edit(data, obj) {
            var id = data.id;
            var name = data.c0;
            var intro = data.c2;
            var avatar = data.c1 || '';
            if (avatar) {
                $('.change-tip').addClass ('none');
                $('.upbtn img').removeClass ('change-img').addClass ("uploadimg");
                $('.upbtn img').attr ('src', avatar);
            } else {
                $('.change-tip').removeClass ('none');
                $('.upbtn img').removeClass ("uploadimg").addClass ('change-img');
                $('.upbtn img').attr ('src', '/ug/mall/assest/images/add-btn.png');
            }
            $('#editdiv [name=id]').val(id);
            $('#editdiv [name=name]').val(name);
            $('#editdiv [name=intro]').val(intro);
            $('#editdiv').dialog('编辑', save);
        }

        function save() {
            var id = $('#editdiv [name=id]').val();
            var name = $('#editdiv [name=name]').val();
            var intro = $('#editdiv [name=intro]').val();
            var avatar = $('.uploadimg').attr('src');
            var purl = '{$url.save}';
            $.ajaxExt(purl, {id: id, name: name, intro: intro, avatar: avatar}, function (r) {
                if (r.isOk()) {
                    window.location.reload();
                } else {
                    $.alert(r.getMessage());
                }
            })
        }

        function getAge(birth) {
            if (! birth || birth == null || birth == '') {
                return '保密';
            }
            var retAge = 0;
            var birthYear = birth.substring(0, 4);
            var birthMonth = birth.substring(4, 6);
            var birthDay = birth.substring(6, 8);

            var d = new Date();
            var nowYear = d.getFullYear();
            var nowMonth = d.getMonth() + 1;
            var nowDay = d.getDay();

            if (birthYear == nowYear) return retAge;
            var ageDiff = nowYear - birthYear;
            if (ageDiff > 0) {
                if (nowMonth == birthMonth) {
                    var dayDiff = nowDay - birthDay;
                    if (dayDiff < 0) {
                        retAge = ageDiff - 1;
                    } else {
                        retAge = ageDiff;
                    }
                } else {
                    var monthDiff = nowMonth - birthMonth;
                    if (monthDiff < 0) {
                        retAge = ageDiff - 1;
                    } else {
                        retAge = ageDiff;
                    }
                }
            } else {
                retAge = '-1';
            }
            return retAge;
        }

        $(function() {
            $('#btn_add').on('click', function() {
                $.confirm('是否同步erp员工', function () {
                    var purl = '{$url.sync}';
                    $.ajaxExt(purl, function (r) {
                        if (r.isOk()) {
                            window.location.reload();
                        } else {
                            $.alert(r.getMessage());
                        }
                    });
                });
            })

            $ ('.upbtn').on ('change', function () {
                $ ('.change-tip').addClass ('none');
                $ ('.upbtn img').removeClass ('change-img').addClass ("uploadimg");
                var formData = new FormData ();
                formData.append ('file', $ ('[name=file]')[0].files[0]);
                $.ajax ({
                    url: '{$url.upload}',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    cache: false,
                    dataType: 'json',
                    success: function (res) {
                        if (res._c == 1) {
                            $ ('.upbtn img').attr ('src', res.data);
                        } else {
                            alert (res._m);
                        }
                    }
                });
            });
        })

    </script>
</block>
<block name="query_ui">
    <div class="form-group query-group">
        <input type="text" class="form-control" cid="q{$k++}" placeholder="搜名称">
    </div>
</block>
<block name="tool_box">
    <button id="btn_add" type="button" class="btn btn-sm btn-primary admin-btn-tab">
        <span class="glyphicon glyphicon-refresh"></span>&nbsp;同步erp员工
    </button>
</block>

<block name="free_area">
    <div id="editdiv" class="spa-page-wrap container-fluid">
        <div style="width: 600px;">
            <form class="form-horizontal" id="edit-optometrist-form">
                <div class="alert alert-error" style="display: none;">
                    <span></span>
                </div>
                <input type="hidden" name="id">
                <input type="hidden" name="team_id">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">头像：</label>
                        <div class="col-sm-9">
                            <div class="imgbox">
                                <div class="upbtn" style="margin-left: 0px;">
                                    <img class="change-img" src="/ug/mall/assest/images/add-btn.png">
                                    <input type="file" name="file">
                                </div>
                                <div class="change-tip">点击上传图片</div>
                            </div>
                            <div class="change-tip">（图片小于25K 推荐50*60px）</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">名称：</label>
                        <div class="col-sm-9">
                            <input type="text" name="name" class="form-control" placeholder="名称" style="width: 150px;">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">简介：</label>
                        <div class="col-sm-9">
                            <textarea class="form-control" rows="5" cols="2" name="intro" placeholder="简介"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</block>