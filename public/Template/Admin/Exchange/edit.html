<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
<block name="title">
<title>汉高微信后台管理</title>
</block>
<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
<link rel="stylesheet" href="/ug/admin/css/dialog.css">
<link rel="stylesheet" href="/ug/b/css/add-coupons.css">
<style type="text/css">
.stores-div {
    width: 490px;
    height: 90px;
    background: #e5e5e5;
    text-align: center;
    line-height: 90px;
}
#store-sel ul {
	list-style: none;
}
.img-view img {
	width: 200px;
	height: auto;
}
.webuploader-pick{
	padding: 0 12px;
	height: 26px;
	line-height: 26px;
	background: #4F88AD;
}
.form-txt{
	font-style: normal;
	margin: 0 4px;
    padding-top: 7px;
    display: inline-block;
}
#coupon-form input{
	border-radius: 4px;
	padding: 6px 12px;
	border: 1px solid #dcdfe6;
}
.space-txt{
	padding: 6px;
    float: left
}
.radio-label{
	float: left;
	margin-right: 4px;
}
.div-box-item{
	margin-top: 4px;
	margin-left: 14px;
	overflow: hidden;
}
.div-box-item .form-control {
	width: 70%;
}
.form-control:focus,.input-default:focus{
	box-shadow: none;
    -webkit-box-shadow: none;
}
</style>
</head>
<body>
	<div class="content-add-body">
		<form class="form-horizontal" id="coupon-form">
			<div class="form-group">
				<label class="col-sm-2 control-label">券名称：</label>
				<div class="col-sm-6">
					<input type="text" class="form-control" name="title" placeholder="填写优惠券名称" style="width: 300px;">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">券类别：</label>
				<div class="col-sm-6">
					<select class="w-normal fleft mr-small form-control" name="classid" style="display: inline-block; width: 30%;"></select>
                    <select class="w-normal fleft mr-small form-control" name="brandid" style="display: inline-block; width: 30%;"></select>
                    <select class="w-normal fleft mr-small form-control" name="breedid" style="display: inline-block; width: 30%;"></select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label coupon_label">每满：</label>
				<div class="col-sm-6 form-inline">
					<input class="input-title form-control" type="text" name="full_money" style="width: 100px;"> 
					&nbsp;<span class="couptype-value">元，减</span>&nbsp;
					<input class="input-title form-control" type="text" name="coupon_value" style="width: 100px;"> 
					&nbsp;<span class="couptype-value2">元</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label coupon_label">所需积分：</label>
				<div class="col-sm-6 form-inline">
					<input class="input-title form-control" type="text" name="exchange_point" style="width: 100px;">
					&nbsp;&nbsp;<span id="ex_ts" style="color: red;display: none">积分设置比例过低</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">发放数量：</label>
				<div class="col-sm-3">
					<input class="form-control" type="text" name="quantity" placeholder="数量" style="width: 100px;">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">每人限领：</label>
				<div class="col-sm-3 form-inline">
					<input class="form-control" type="text" name="take_limit" placeholder="数量" style="width: 100px;"> &nbsp;张
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-2 control-label">可兑换时间：</label>-->
<!--				<div class="col-sm-6">-->
<!--					<div class="div-box">-->
<!--						<label class="radio-label">-->
<!--							<input class="radio-input" type="radio" name="effect_type" value="1" checked="checked">&nbsp;-->
<!--							<span class="radioInput"></span>固定日期-->
<!--						</label>-->
<!--						<div class="input-group">-->
<!--							<input type="text" class="w20 form-control" id="ex-deadline-date" name="ex_start_end_date">-->
<!--						</div>-->
<!--					</div>-->
<!--					<div class="div-box">-->
<!--						<label class="radio-label">-->
<!--							<input class="radio-input" type="radio" name="effect_type" value="2">&nbsp;-->
<!--							<span class="radioInput"></span>永久有效-->
<!--						</label>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-2 control-label">有效期：</label>
				<div class="col-sm-6">
					<div class="div-box">
					    <label class="radio-label">
					        <input class="radio-input" type="radio" name="deadline_type" value="0" checked="checked">&nbsp;
					        <span class="radioInput"></span>固定日期
					    </label>
					    <div class="input-group">
					        <input type="text" class="w20 form-control" id="deadline-date" name="start_end_date">
					    </div>
					</div>
					<div class="div-box">
					    <label class="radio-label">
					        <input class="radio-input" type="radio" name="deadline_type" value="1">&nbsp;
					        <span class="radioInput"></span>领券后
					    </label>
					    <div class="input-group">
					        <div class="div-box-item">
					            <input type="text" class="w20 form-control" name="day_valid"><em class="form-txt">天后生效</em>
					        </div>
					        <div class="div-box-item">
					            <input type="text" class="w20 form-control" name="day_invalid"><em class="form-txt">天后失效</em>
					        </div>
					    </div>
					</div>
				</div>
			</div>
<!--			<div class="form-group">-->
<!--				<label class="col-sm-2 control-label">券类型：</label>-->
<!--				<div class="col-sm-4">-->
<!--					<label class="radio-inline">-->
<!--						<input type="radio" class=""  name="type" value="1" checked>线下优惠券-->
<!--					</label>-->
<!--					<label class="radio-inline">-->
<!--						<input type="radio" class="" name="type" value="2">线上优惠券-->
<!--					</label>-->
<!--					<label class="radio-inline">-->
<!--						<input type="radio" class="" name="type" value="3">通用优惠券-->
<!--					</label>-->
<!--				</div>-->
<!--			</div>-->
<!--			<div class="form-group" id="range" style="display: none">-->
<!--				<label class="col-sm-2 control-label">线上使用范围：</label>-->
<!--				<div class="col-sm-4">-->
<!--					<label class="radio-inline">-->
<!--						<input type="radio" class=""  name="use_range" value="1" checked>全部商品可用-->
<!--					</label>-->
<!--					<label class="radio-inline">-->
<!--						<input type="radio" class="" name="use_range" value="2">指定商品可用-->
<!--					</label>-->
<!--				</div>-->
<!--			</div>-->
			<div class="form-group">
				<label class="col-sm-2 control-label">适用商品：</label>
				<div class="col-sm-6">
					<textarea class="textarea-default form-control" name="scope" placeholder="适用商品"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">使用须知：</label>
				<div class="col-sm-6">
					<textarea class="textarea-default form-control" name="remark" placeholder="使用须知"></textarea>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label"></label>
				<div class="col-sm-3">
					<a href="javascript:void(0);" class="btn btn-default-bg save">保存</a>
				</div>
			</div>
		</form>
	</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript" src="/manager/laydate/laydate.js"></script>

<block name="js"></block>
<script type="text/javascript">
	// 0 - 代价 1 - 折扣
	var coupType = "#{$ctype}#";
	var inf = "#{$inf}#";

    $(function () {
    	load_form(inf);
    })

    function load_form(inf) {
    	var form_data = inf;
		for(var i in form_data) {
			switch (i) {
				case 'deadline_type':
					$('[name="' + i + '"][value="' + form_data[i] + '"]').attr('checked', 'checked');
					break;
				case 'effect_type':
					$('[name="' + i + '"][value="' + form_data[i] + '"]').attr('checked', 'checked');
					break;
				case 'start_date':
					var opt = {
						elem: '#deadline-date',
						range: true
					};
					console.log(form_data)
					if (form_data[i]!= '' && form_data[i] != 0 && form_data[i] != null) {
						var start_date = form_data['start_date'];
						var end_date = form_data['end_date'];
						opt.value = start_date + ' - ' + end_date;
					}
					laydate.render(opt);
					break;
				case 'exchange_start':
					var opt = {
						elem: '#ex-deadline-date',
						range: true
					};

					if (form_data[i] != 0 && form_data[i] != null && form_data[i]!='') {
						opt.value = form_data['exchange_start'] + ' - ' + form_data['exchange_end'];
					}
					laydate.render(opt);
					break;
				case 'local_url':
                    $('[name="' + i + '"]').val(form_data[i]);
                    $('.img-view').html('<img src="' + form_data[i] + '">');
					break;

                case 'classid':
                    load_class(form_data['classid'], function() {
                        load_brand(form_data['classid'], form_data['brandid'], function() {
                            load_breed(form_data['brandid'], form_data['breedid'], function() {});
                        });
                    });
                    break;
				case 'type':
					var type = form_data[i];
					$("input[name='type'][value="+ type +"]").attr("checked",true);
					if (type == 1)
					{
						$("#range").css('display','none');
					}else{
						$("#range").css('display','block');
					}
					break;
				case 'use_range':
					var use_range = form_data[i];
					$("input[name='use_range'][value="+ use_range +"]").attr("checked",true);
					break;
				default:
					$('#coupon-form input[name="' + i + '"]').val(form_data[i]);
					$('#coupon-form select[name="' + i + '"]').val(form_data[i]);
					$('#coupon-form textarea[name="' + i + '"]').val(form_data[i]);
					break;
			}
		}
    }

	$('.save').click(function() {
	    var form = $('#coupon-form').serializeArray();
		tempForm = format_data(form)
	    if ($.trim(tempForm.title) == '') {
	    	$.alert('请填写券名称');
	    	return;
	    }
	    if (tempForm.classid == '0' || tempForm.classname == '请选择') {
	    	$.alert('请选择券类型1');
	    	return;
	    }
	    if (tempForm.brandid == '0' || tempForm.brandname == '请选择') {
	    	$.alert('请选择券类型2');
	    	return;
	    }
	    if (tempForm.breedid == '0' || tempForm.breedname == '请选择') {
	    	$.alert('请选择券类型3');
	    	return;
	    }
	    if (tempForm.full_money < 0) {
	    	$.alert('满减不能小于0');
	    	return;
	    }
	    if (tempForm.coupon_value <= 0) {
	    	$.alert('面值不能小于0');
	    	return;
	    }

		if (tempForm.classid == coupType[1] && parseFloat(tempForm.coupon_value) >= 10) {
			//折扣券
			$.alert('折扣必须小于10');
			return;
		}
		if (tempForm.exchange_point <= 0) {
			$.alert('请输入所需积分');
			return;
		}
		if (tempForm.deadline_type == 0 && (tempForm.start_date == '' || tempForm.end_date == ''))
		{
			$.alert('请设置有效期');
			return;
		}
		if (tempForm.deadline_type == 1 && (tempForm.day_valid == '' || tempForm.day_valid == ''))
		{
			$.alert('请设置有效期');
			return;
		}
		checkExchangePoint();

	    $.ajaxExt('{$url.save}', tempForm, function(res) {
	        if (res.isOk()) {
	        	$.alert('编辑成功', function() {
	        		window.location.reload();
	        	});
	        } else {
	            $.alert(res.getMessage());
	        }
	    });
	});

	// 格式化数据
	function format_data(form) {
	    var form_data = {};

	    for (var i=0; i<form.length; i++) {
			form_data[form[i].name] = form[i].value;
	    }

	    for (var i in form_data) {
	        if (i == 'start_end_date') {
	            var date_arr = form_data[i].split(' - ');
	            form_data['start_date'] = $.trim(date_arr[0]);
	            form_data['end_date'] = $.trim(date_arr[1]);
	            delete form_data['start_end_date'];
	        }
			if (i == 'ex_start_end_date') {
				var ex_date_arr = form_data[i].split(' - ');
				form_data['exchange_start'] = $.trim(ex_date_arr[0]);
				form_data['exchange_end'] = $.trim(ex_date_arr[1]);
				delete form_data['ex_start_end_date'];
			}
	    }

	    form_data['classname'] = $('[name=classid] option:selected').text();
	    form_data['brandname'] = $('[name=brandid] option:selected').text();
	    form_data['breedname'] = $('[name=breedid] option:selected').text();
	    form_data['id'] = inf.id;

	    return form_data || [];
	}

	$('[name=type]').change(function() {
		var type = $(this).val();
		if (type == 1) {
			$("#range").css('display','none');
			$("#receive_time").css('display','none');
		}else{
			$("#range").css('display','block');
			$("#receive_time").css('display','block');
		}
	});

	function load_class(id = null, callBack) {
	    $.post('{$url.class}', {action: 'class'}, function(res) {
	        if (res._c == 0) {
	            var option = '<option value="0">请选择</option>';
	            var item = res.data, length = item.length;
	            for (var i=0; i<length; i++) {
	                var sel = '';
	                if (id && item[i].classid == id) {
	                    sel = ' selected="selected"';
	                }
	                option += '<option value="' + item[i].classid + '" ' + sel + '>' + item[i].name + '</option>';
	            }
	            $('[name=classid]').html(option);
	        }
	        callBack();
	    }, 'json');
	}

	function load_brand(classid = null, id = null, callBack) {
		if(classid == coupType[1]){
			// 折扣券
			$('.couptype-value').html('元，打');
			$('.couptype-value2').html('折 （1 - 9.9）');
		}
		if(classid == coupType[0]) {
			// 代价券
			$('.couptype-value').html('元，减');
			$('.couptype-value2').html('元');
		}
	    $.post('{$url.class}', {action: 'brand', 'clsid': classid}, function(res) {
	        if (res._c == 0) {
	            var option = '<option value="0">请选择</option>';
	            if (res.data) {
	            	item = res.data, length = item.length;
	                for (var i=0; i<length; i++) {
	                    var sel = '';
	                    if (id && item[i].brandid == id) {
	                        sel = ' selected="selected"';
	                    }
	                    option += '<option value="' + item[i].brandid + '" ' + sel + '>' + item[i].name + '</option>';
	                }
	            }
	            $('[name=brandid]').html(option);
	            callBack();
	        }
	    }, 'json');
	}

	function load_breed(brandid = null, id, callBack) {
	    $.post('{$url.class}', {action: 'breed', 'braid': brandid}, function(res) {
	        if (res._c == 0) {
	            var option = '<option value="0">请选择</option>';
	            if (res.data) {
	            	var item = res.data, length = item.length;
	                for (var i=0; i<length; i++) {
	                    var sel = '';
	                    if (id && item[i].breedid == id) {
	                        sel = ' selected="selected"';
	                    }
	                    option += '<option value="' + item[i].breedid + '" ' + sel + '>' + item[i].name + '</option>';
	                }
	            }
	            $('[name=breedid]').html(option);
	            callBack();
	        }
	    }, 'json');
	}

	$(document).on('change', '[name=classid]', function() {
	    var classid = $(this).val();
	    load_brand(classid, null, function() {
	        $('[name=brandid] option').eq(0).change();
	    });
	});

	$(document).on('change', '[name=brandid]', function() {
	    var brandid = $(this).val();
	    load_breed(brandid, null, function() {
	        $('[name=breedid] option').eq(0).change();
	    });
	});

	$(document).on('blur', '[name=coupon_value]', function() {
		checkExchangePoint();
	});

	$(document).on('blur', '[name=exchange_point]', function() {
		checkExchangePoint();
	});

	function checkExchangePoint() {
		var exchange_point = $('input[name=exchange_point]').val();
		var coupon_value = $('input[name=coupon_value]').val();
		var classid = $('select[name=classid]').val();
		if (classid == coupType[0] && coupon_value > 0 && exchange_point > 0)
		{
			var rate = coupon_value/exchange_point;
			if (rate >= 0.1)
			{
				$('#ex_ts').css('display','inline-block');
			}else{
				$('#ex_ts').css('display','none');
			}
		}
	}
</script>
</html>