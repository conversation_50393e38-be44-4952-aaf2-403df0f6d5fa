<extend name="Base@Common/listtable" />
<block name="title">
<title>评价列表</title>
</block>
<block name="css">
<style type="text/css">
.keywords {
	padding: 30px 0;
}
.keywords .keys, .keywords .btns {
	display: inline-block;
}
.keywords .keys p {
	display: inline-block;
	margin-right: 10px;
}
.keywords .k {
	width: 142px;
}
</style>
</block>
<block name="js">
<script type="text/javascript" src="/manager/laydate/laydate.js"></script>
<script type="text/javascript" src="/ug/js/bootstrap.min.js"></script>
<script type="text/javascript">
	inf_dtbl.cols = [{
		tit: '用户',
		rep: function(d) {
			return d.uname;
		}
	}, {
		tit: '交易时间',
		rep: function(d) {
			return d.cdate
		}
	}, {
		col: 'c1',
		tit: '评价时间',
		rep: function(d) {
			return d.c1.date('yyyy-MM-dd hh:mm:ss');
		}
	}, {
		tit: '订单总金额',
		rep: function(d) {
			return d.price
		}
	}, {
		col: 'c2',
		tit: '等级',
		rep: function(d) {
			if (d.c2 == 1) {
				return '好评';
			} else if (d.c2 == 2) {
				return '中评';
			} else if (d.c2 == 3) {
				return '差评';
			} else {}
		}
	}, {
		col: 'c3',
		tit: '标签'
	}, {
		col: 'c4',
		tit: '其它意见'
	}];

	$(function() {
		laydate.render({
		  elem: '[cid=q0]'
		});
		laydate.render({
		  elem: '[cid=q1]'
		});

		$('.s').html('<span>好评：' + inf_dtbl.praise + '</span>&nbsp;&nbsp;&nbsp;&nbsp;<span>中评：' + inf_dtbl.average + '</span>&nbsp;&nbsp;&nbsp;&nbsp;<span>差评：' + inf_dtbl.bad + '</span>');

		$('.addkey').click(function() {
			$(this).popover({
				title: '添加关键词',
				html: true,
				trigger: 'manual',
				content: '<form class="form-inline"><div class="form-group"><input class="form-control input-sm k"></div>&nbsp;<a href="javascript:;" class="btn btn-default btn-sm" onclick="add()">保存</a>&nbsp;<a href="javascript:;" class="btn btn-danger btn-sm" onclick="cancel()">取消</a></form>'
			}).popover('toggle');
		});

		if (inf_dtbl.keys.length > 0) {
			var keys = inf_dtbl.keys, length = keys.length;
			if (length >= 8) {
				$('.addkey').hide();
			}
			var htm = '';
			for (var i=0; i<length; i++) {
				htm += '<p><span class="label label-primary">' + keys[i].keyword + '</span><i class="glyphicon glyphicon-remove-sign" onclick="del(' + keys[i].id + ', this)"></i></p>';
			}
			$('.keys').html(htm);
		}
	})

	function add() {
		var key = $('.k').val();
		if (key && key != '') {
			$.post('{$url.addkey}', {key: key}, function(res) {
				if (res._c == 0) {
					$('.keys').append('<p><span class="label label-primary">' + key + '</span><i class="glyphicon glyphicon-remove-sign" onclick="del(' + res.data + ', this)"></i></p>');
					if ($('.keys p').length == 8) {
						$('.addkey').hide();
					}
				} else {
					$.alert(res._m);
				}
			}, 'json');
		}
		$('.addkey').popover('hide');
	}

	function del(id, obj) {
		var self = $(obj);
		$.post('{$url.delkey}', {id: id}, function(res) {
			if (res._c == 0) {
				self.parents('p').remove();
				if ($('.keys p').length < 8) {
					$('.addkey').show();
				}
			} else {
				$.alert(res._m);
			}
		}, 'json');
	}

	function cancel() {
		$('.addkey').popover('hide');
	}

</script> 
</block>

<block name="query_ui">
	<div class="form-group">
	    <label></label>
		<input type="text" cid="q0" class="form-control" placeholder="评价开始时间">
		<input type="text" cid="q1" class="form-control" placeholder="评价结束时间">
	</div>
    <div class="form-group">
        <label></label>
        <select class="form-control" cid="q2">
        	<option value="0">全部</option>
        	<option value="1">好评</option>
        	<option value="2">中评</option>
        	<option value="3">差评</option>
        </select>
    </div>
</block>
<block name="tool_box">
	<div class="s"></div>
</block>
<block name="panel">
	<div class="keywords">
		添加关键词：
		<div class="keys"></div>
		<div class="btns">
			<a href="javascript:;" class="btn btn-default btn-xs addkey">添加</a>
		</div>
	</div>
</block>