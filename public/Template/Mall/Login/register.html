<!DOCTYPE html>
<html lang="zh-CN" class="ui-page-login">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<title>注册</title>
	<link rel="stylesheet" href="/ug/mall/css/mui.min.css">
	<link rel="stylesheet" href="/ug/mall/assest/css/iconfont.css">
	<link rel="stylesheet" type="text/css" href="/ug/mall/css/mui.picker.min.css" />
	<style>
		.mall-register {
			padding: 0 12px;
		}

		.register-box {
			padding-bottom: 30px;
		}

		.flex-box-align-center {
			display: -webkit-flex;
			display: flex;
			-webkit-justify-content: center;
			justify-content: center;
			-webkit-align-items: center;
			align-items: center;
		}

		.register-box .mall-input-row {
			margin: 30px 0;
			border-radius: 10px;
			background-color: rgb(255, 255, 255);
			box-shadow: 0px 1px 0px 0px rgba(10, 8, 3, 0.1);
			display: -webkit-flex;
			display: flex;
			-webkit-justify-content: center;
			justify-content: center;
			-webkit-align-items: center;
			align-items: center;
			height: 48px;
		}

		.mall-input-inner {
			-webkit-flex: 1;
			flex: 1;
		}

		.register-box .mall-input-row>span,
		.mall-input-inner>span {
			width: 40px;
			text-align: center;
			font-size: 18px;
		}

		.mui-input-row.mall-input-row>input,
		.mall-input-inner>input {
			-webkit-flex: 1;
			flex: 1;
			margin-bottom: 0;
			border: 0;
			font-size: 14px;
			padding-left: 0;
			text-align: left;
			padding: 0 12px 0 0;
		}

		.btn-register {
			width: 100%;
			height: 48px;
			line-height: 48px;
			font-size: 18px;
			margin-top: 30px;
			border-radius: 10px;
			text-align: center;
			border: rgb(25, 137, 209);
			background-color: rgb(25, 137, 209);
			box-shadow: 0px 2px 4px 0px rgba(10, 8, 3, 0.2);
		}

		.get-code-box {
			width: 80px;
			height: 48px;
			line-height: 48px;
			text-align: center;
		}

		.get-code-box>input[type=button] {
			border: 0;
			color: #fff;
			height: inherit;
			line-height: inherit;
			font-size: 14px;
			padding: 0;
			font-weight: normal;
			width: 100%;
			background-color: rgb(25, 137, 209);
			border-radius: 0 10px 10px 0;
		}

		.get-code-box>.btn-get-code.bg-gray {
			background-color: rgb(152, 152, 152);
		}

		.get-code-box>input[type=button]:focus {
			background-color: rgb(25, 137, 209);
		}

		input[type=number]::-webkit-inner-spin-button,
		input[type=number]::-webkit-outer-spin-button {
			-webkit-appearance: none;
			appearance: none;
			margin: 0;
		}

		/* checkbox */

		.check-item.mui-left input[type=checkbox] {
			left: 0;
			top: 7px;
		}

		.check-item.mui-left label {
			padding-left: 26px;
			padding-right: 6px;
			font-size: 14px;
		}

		.check-item.active input[type=checkbox]:before,
		.check-item.active input[type=checkbox]:checked:before {
			content: '\e442';
			color: rgb(25, 137, 209);
		}

		.check-item input[type=checkbox]:before {
			font-size: 22px;
		}

		.check-item input[type=checkbox]:checked:before {
			content: '\e411';
			color: #aaa;
		}

		.check-item a {
			display: inline-block;
			color: rgb(25, 137, 209);
		}

		/* checkbox end */

		.mui-dtpicker-header button {
			border: 0;
			background-color: transparent;
			text-align: left;
			-webkit-flex: 1;
			flex: 1;
			font-size: 15px;
			color: rgb(152, 152, 152);
		}

		.mui-dtpicker-header button:last-child {
			color: rgb(25, 137, 209);
		}

		.mui-btn-blue,
		.mui-btn-primary,
		input[type=submit] {
			border: rgb(25, 137, 209);
			background-color: rgb(25, 137, 209);
		}

		.btn-login {
			text-align: center;
		}

		.btn-login {
			margin-top: 40px;
		}

		.btn-login>a {
			padding: 5px 6px;
			font-size: 16px;
			color: rgb(59, 58, 58);
			border-bottom: 1px solid rgb(201, 201, 201);
		}
	</style>

</head>

<body>
	<div class="mui-content mall-register">
		<div class="register-wrap">
			<div class="register-box">
				<!-- <div class="mui-input-row mall-input-row">
						<span class="iconfont icon-wode"></span>
						<input id="account" class="mui-input-clear mui-input" type="text" placeholder="请输入您的姓名">
					</div> -->
				<div class="mui-input-row mall-input-row">
					<span class="iconfont icon-shouji"></span>
					<input id="mobile" class="mui-input-clear mui-input" type="number" pattern="[0-9]*" placeholder="请输入手机号">
				</div>
				<!-- <div class="mui-input-row mall-input-row">
						<span class="iconfont icon-riqi1"></span>
						<input id="birthday" class="mui-input-clear mui-input mall-btn-date" type="text" placeholder="请选择你的生日">
					</div> -->
				<div class="mall-input-row">
					<div class="mui-input-row flex-box-align-center mall-input-inner">
						<span class="iconfont icon-yanzhengma"></span>
						<input id="code" class="mui-input-clear mui-input" type="text" placeholder="请输入验证码">
					</div>
					<div class="get-code-box">
						<input class="btn-get-code" type="button" value="获取验证码" />
					</div>
				</div>
			</div>
			<!-- 用户协议同意区域 -->
			<div class="agreement-box" style="margin: 20px 0; font-size: 14px; color: #999;">
				<div style="display: flex; align-items: flex-start;">
					<input type="radio" id="agreement-checkbox"
						style="margin-right: 8px; margin-top: 2px; transform: scale(1.2);">
					<div style="flex: 1; line-height: 1.4;">
						<span>已阅读并同意以下协议</span>
						<a href="#" onclick="showUserAgreement(); return false;"
							style="color: rgb(25, 137, 209); text-decoration: none; margin-left: 4px;">《用户及隐私协议》</a>
					</div>
				</div>
			</div>
			<div class="mui-btn-primary btn-register">注册</div>
			<div class="btn-login">
				<a href="{$url.login}">去登录</a>
			</div>
		</div>
	</div>
	<script src="/ug/js/jquery.min.js"></script>
	<script src="/ug/mall/js/mui.min.js"></script>
	<script src="/ug/mall/js/mui.picker.min.js"></script>
	<script src="/ug/mall/assest/js/mui.utils.js"></script>
	<script>
		$(function () {
			mui.init();
			//				$('.register-box input').on('blur', function() { 
			//					document.activeElement.blur();
			//				});

			$('#mobile-input').submit(function () {
				return false;
			});
			mui('.register-box').on('tap', '#mobile', function () {
				var u = navigator.userAgent;
				var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
				if (isAndroid == true) {
					$('#mobile').attr('pattern', '\d');
					$('#mobile').attr('type', "text");
				}
			});
			mui('.register-wrap').on('tap', '.btn-login>a', function () {
				window.location.href = this.href;
			});
			//				$('#mobile').on('keydown', function(e) {
			//					if(e.keyCode == 13) {
			//						document.activeElement.blur();
			//					}
			//				});
			mui('.register-box').on('tap', '#birthday', function () {
				document.activeElement.blur();
				var _this = $(this);
				var optionsJson = this.getAttribute('data-options') || '{}';
				var options = JSON.parse(optionsJson);
				var id = this.getAttribute('id');
				picker = new mui.DtPicker({
					type: "date",
					beginDate: new Date(1940, 04, 25), //设置开始日期 
					endDate: new Date(), //设置结束日期 
					customData: options
				});
				picker.setSelectedValue("1988-01-01");
				picker.show(function (rs) {
					_this.val(rs.text);
					picker.dispose();
					picker = null;
				});
			});

			mui('.register-wrap').on('tap', '.get-code-box > .btn-get-code', setCode);
			mui('.register-wrap').on('tap', '.btn-register', registerInfo);
		});

		function registerInfo() {
			document.activeElement.blur();
			var account = $('#account').val(),
				mobile = $('#mobile').val(),
				birthday = $('#birthday').val(),
				code = $('#code').val();

			/*验证用户信息是否为空*/
			if (!check({
				// account: account,
				mobile: mobile,
				// birthday: birthday,
				code: code,
			})) return false;
			// 检查是否同意用户协议
			if (!$('#agreement-checkbox').is(':checked')) {
				mui.toast('请先同意用户及隐私协议');
				return false;
			}
			var turl = "";
			var pdata = {
				// c0: account,
				c0: mobile,
				// c2: birthday,
				c1: code
			};
			$.ajaxExt(turl, pdata, function (jo) {
				if (jo._c == 1) {
					window.location.href = '{$url.registerinfo}' + '&phone=' + mobile;
				} else if (jo._c == 2) {
					window.location.href = '{$url.registerlist}' + '&phone=' + mobile;
				} else {
					mui.toast(jo._m);
				}
			});

		}

		function setCode() {
			document.activeElement.blur();
			var mobile = $('#mobile').val();
			/*验证用户信息是否为空*/
			if (isEmpty(mobile)) {
				mui.toast('手机号不能为空');
				return false;
			};
			if (!isValidPhone(mobile)) {
				mui.toast('请填写正确的手机号');
				return false;
			}
			var turl = '{$url.getcode}';
			var pdata = {
				'mobile': mobile
			}
			time($('.btn-get-code'));
			$.ajaxExt(turl, pdata, function (jo) {
				if (jo.isOk()) {
					mui.toast(jo.getMessage());
				} else {
					mui.alert(jo.getMessage());
				}
			});
		};
		var wait = 60;

		function time(btn) {
			if (wait == 0) {
				btn.removeClass("bg-gray");
				btn.removeAttr("disabled");
				btn.val("获取验证码");
				wait = 60;
			} else {
				btn.addClass("bg-gray");
				btn.attr("disabled", true);
				btn.val(wait + "秒重发");
				wait--;
				setTimeout(function () {
					time(btn);
				}, 1000);
			}
		};

		function isEmpty(val) {
			return val === ''
		};

		function check_userName(account) {
			var _max = 4;
			var _cur = getByteLen(account);
			if (_cur > _max) {
				return false;
			}
			return true;
		};

		function getByteLen(val) { // 返回val的字节长度
			var len = 0;
			for (var i = 0; i < val.length; i++) {
				len += 1;
			}
			return len;
		};

		function check(obj) {
			if (isEmpty(obj.mobile)) {
				mui.toast('手机号不能为空');
				return false;
			};
			if (!isValidPhone(obj.mobile)) {
				mui.toast('手机号不对');
				return false;
			}
			if (isEmpty(obj.code)) {
				mui.toast('验证码不能为空');
				return false;
			};
			return true
		};

		function isValidPhone(val) { //手机号校验
			return /^1[0-9]{10}$/.test(val);
		};

		// 显示用户及隐私协议
		function showUserAgreement() {
			window.location.href = '/Home/Index/policy'
		}
	</script>
</body>

</html>